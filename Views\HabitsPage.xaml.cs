using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Windows.Media.Animation;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views
{
    public partial class HabitsPage : Page
    {
        public ObservableCollection<Habit> Habits { get; set; }

        public HabitsPage()
        {
            InitializeComponent();
            Habits = new ObservableCollection<Habit>();
            DataContext = this;
            LoadHabits();
        }

        private void LoadHabits()
        {
            Habits.Clear();
            var habits = DatabaseService.GetAllHabits();
            
            // Sort habits by name
            var sortedHabits = habits.OrderBy(h => h.Name).ToList();
            
            for (int i = 0; i < sortedHabits.Count; i++)
            {
                var habit = sortedHabits[i];
                // Generate calendar days for each habit
                GenerateCalendarDays(habit);
                
                // Add habit with animation
                var habitCard = new Border();
                habitCard.Opacity = 0;
                habitCard.RenderTransform = new ScaleTransform(0.8, 0.8);
                
                // Create animations
                var fadeAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(350),
                    BeginTime = TimeSpan.FromMilliseconds(i * 120) // Stagger the animations
                };
                
                var scaleXAnimation = new DoubleAnimation
                {
                    From = 0.8,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(350),
                    BeginTime = TimeSpan.FromMilliseconds(i * 120),
                    EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                };
                
                var scaleYAnimation = new DoubleAnimation
                {
                    From = 0.8,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(350),
                    BeginTime = TimeSpan.FromMilliseconds(i * 120),
                    EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                };
                
                Habits.Add(habit);
                
                // Start animations after the item is added to the collection
                Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                    habitCard.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);
                    ((ScaleTransform)habitCard.RenderTransform).BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
                    ((ScaleTransform)habitCard.RenderTransform).BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
                }));
            }
        }

        private void GenerateCalendarDays(Habit habit)
        {
            var today = DateTime.Today;
            var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            
            // Calculate the offset for the first day of the month
            int offset = ((int)firstDayOfMonth.DayOfWeek);
            
            // Create calendar days
            var calendarDays = new ObservableCollection<CalendarDay>();
            
            // Add empty days for the offset
            for (int i = 0; i < offset; i++)
            {
                calendarDays.Add(new CalendarDay { Day = "", Background = Brushes.Transparent });
            }
            
            // Add days of the month with animation properties
            for (int day = 1; day <= lastDayOfMonth.Day; day++)
            {
                var currentDate = new DateTime(today.Year, today.Month, day);
                var isTracked = habit.IsTrackedOnDate(currentDate);
                var isToday = currentDate.Date == today.Date;
                
                var background = isTracked ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319")) : 
                                isToday ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")) : 
                                Brushes.Transparent;
                
                // Add animation properties to calendar days
                var calendarDay = new CalendarDay { 
                    Day = day.ToString(), 
                    Background = background, 
                    Date = currentDate,
                    // Animation properties will be used in the UI
                    AnimationDelay = day * 20 // Staggered animation delay
                };
                
                calendarDays.Add(calendarDay);
            }
            
            // Fill remaining cells to complete the grid (5 rows x 7 columns = 35 cells)
            int remainingCells = 35 - calendarDays.Count;
            for (int i = 0; i < remainingCells; i++)
            {
                calendarDays.Add(new CalendarDay { Day = "", Background = Brushes.Transparent });
            }
            
            habit.CalendarDays = calendarDays;
        }

        private void AddHabitButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(HabitNameInput.Text))
            {
                ShowSnackbar("يرجى إدخال اسم العادة");
                return;
            }

            var newHabit = new Habit
            {
                Name = HabitNameInput.Text,
                Description = HabitDescriptionInput.Text,
                CreatedAt = DateTime.Now
            };

            DatabaseService.AddHabit(newHabit);
            newHabit.Id = 0; // Will be set by database
            ShowSnackbar("تمت إضافة العادة بنجاح");

            // Clear inputs
            HabitNameInput.Text = string.Empty;
            HabitDescriptionInput.Text = string.Empty;

            // Refresh habits list
            LoadHabits();
        }

        private void DeleteHabitButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var habit = button.DataContext as Habit;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف العادة '{habit.Name}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DatabaseService.DeleteHabit(habit.Id);
                ShowSnackbar("تم حذف العادة بنجاح");
                LoadHabits();
            }
        }

        private void HabitToggle_Checked(object sender, RoutedEventArgs e)
        {
            var toggleButton = sender as ToggleButton;
            var habit = toggleButton.DataContext as Habit;
            
            // Track habit for today
            DatabaseService.TrackHabit(habit.Id, DateTime.Today);
            ShowSnackbar($"تم تسجيل إنجاز '{habit.Name}' لليوم");
            LoadHabits();
        }

        private void HabitToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            var toggleButton = sender as ToggleButton;
            var habit = toggleButton.DataContext as Habit;
            
            // Untrack habit for today
            DatabaseService.UntrackHabit(habit.Id, DateTime.Today);
            ShowSnackbar($"تم إلغاء تسجيل إنجاز '{habit.Name}' لليوم");
            LoadHabits();
        }

        private void ShowSnackbar(string message)
        {
            var snackbar = new Snackbar();
            snackbar.MessageQueue.Enqueue(message, null, null, null, false, true, TimeSpan.FromSeconds(3));
        }
    }

    public class CalendarDay
    {
        public string Day { get; set; }
        public Brush Background { get; set; }
        public DateTime? Date { get; set; }
    }
}