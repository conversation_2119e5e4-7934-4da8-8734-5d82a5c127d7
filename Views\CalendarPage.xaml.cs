using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views
{
    public partial class CalendarPage : Page
    {
        private DateTime _currentMonth;
        private DateTime _selectedDate;

        public ObservableCollection<CalendarDayViewModel> CalendarDays { get; set; }
        public ObservableCollection<TaskItem> SelectedDayTasks { get; set; }
        public ObservableCollection<Habit> SelectedDayHabits { get; set; }

        public CalendarPage()
        {
            InitializeComponent();
            _currentMonth = DateTime.Today;
            _selectedDate = DateTime.Today;
            
            CalendarDays = new ObservableCollection<CalendarDayViewModel>();
            SelectedDayTasks = new ObservableCollection<TaskItem>();
            SelectedDayHabits = new ObservableCollection<Habit>();
            
            DataContext = this;
            
            GenerateCalendar();
            UpdateSelectedDayText();
            LoadSelectedDayItems();
        }

        private void GenerateCalendar()
        {
            CalendarDays.Clear();
            
            // Update month/year display
            MonthYearText.Text = _currentMonth.ToString("MMMM yyyy");
            
            // Get first day of the month
            var firstDayOfMonth = new DateTime(_currentMonth.Year, _currentMonth.Month, 1);
            
            // Calculate the offset for the first day of the month
            int offset = ((int)firstDayOfMonth.DayOfWeek);
            
            // Get last day of the month
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            
            // Get all tasks for the month
            var tasks = DatabaseService.GetAllTasks()
                .Where(t => t.DueDate.Year == _currentMonth.Year && t.DueDate.Month == _currentMonth.Month)
                .ToList();
            
            // Get all habits
            var habits = DatabaseService.GetAllHabits();
            
            // Add empty days for the offset
            for (int i = 0; i < offset; i++)
            {
                CalendarDays.Add(new CalendarDayViewModel { Day = "", Background = Brushes.Transparent });
            }
            
            // Add days of the month
            for (int day = 1; day <= lastDayOfMonth.Day; day++)
            {
                var currentDate = new DateTime(_currentMonth.Year, _currentMonth.Month, day);
                var isToday = currentDate.Date == DateTime.Today.Date;
                var isSelected = currentDate.Date == _selectedDate.Date;
                
                // Get tasks for this day
                var dayTasks = tasks.Where(t => t.DueDate.Day == day).ToList();
                
                // Get habits tracked for this day
                var dayHabits = habits.Where(h => h.IsTrackedOnDate(currentDate)).ToList();
                
                // Create indicators
                var indicators = new List<DayIndicator>();
                
                // Add task indicators by priority
                if (dayTasks.Any(t => t.Priority == TaskPriority.High))
                    indicators.Add(new DayIndicator { Color = new SolidColorBrush(Colors.Red) });
                if (dayTasks.Any(t => t.Priority == TaskPriority.Medium))
                    indicators.Add(new DayIndicator { Color = new SolidColorBrush(Colors.Orange) });
                if (dayTasks.Any(t => t.Priority == TaskPriority.Low))
                    indicators.Add(new DayIndicator { Color = new SolidColorBrush(Colors.Green) });
                
                // Add habit indicator
                if (dayHabits.Any())
                    indicators.Add(new DayIndicator { Color = new SolidColorBrush(Colors.Blue) });
                
                // Set background and border
                var background = isSelected ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319")) : 
                                 Brushes.Transparent;
                
                var borderBrush = isToday ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#262626")) : 
                                  Brushes.LightGray;
                
                var borderThickness = isToday ? new Thickness(2) : new Thickness(1);
                
                var fontWeight = isToday || isSelected ? FontWeights.Bold : FontWeights.Normal;
                
                CalendarDays.Add(new CalendarDayViewModel
                {
                    Day = day.ToString(),
                    Date = currentDate,
                    Background = background,
                    BorderBrush = borderBrush,
                    BorderThickness = borderThickness,
                    DayFontWeight = fontWeight,
                    Indicators = indicators
                });
            }
            
            // Fill remaining cells to complete the grid (6 rows x 7 columns = 42 cells)
            int remainingCells = 42 - CalendarDays.Count;
            for (int i = 0; i < remainingCells; i++)
            {
                CalendarDays.Add(new CalendarDayViewModel { Day = "", Background = Brushes.Transparent });
            }
            
            // Add click event to calendar days
            CalendarItemsControl.ItemContainerGenerator.StatusChanged += (sender, e) =>
            {
                if (CalendarItemsControl.ItemContainerGenerator.Status == System.Windows.Controls.Primitives.GeneratorStatus.ContainersGenerated)
                {
                    for (int i = 0; i < CalendarDays.Count; i++)
                    {
                        var container = CalendarItemsControl.ItemContainerGenerator.ContainerFromIndex(i) as FrameworkElement;
                        if (container != null && CalendarDays[i].Date.HasValue)
                        {
                            var date = CalendarDays[i].Date.Value;
                            container.MouseLeftButtonDown += (s, args) => CalendarDay_Click(date);
                        }
                    }
                }
            };
        }

        private void CalendarDay_Click(DateTime date)
        {
            _selectedDate = date;
            UpdateSelectedDayText();
            LoadSelectedDayItems();
            GenerateCalendar(); // Regenerate to update selected day highlighting
        }

        private void UpdateSelectedDayText()
        {
            SelectedDayText.Text = _selectedDate.ToString("dddd, d MMMM yyyy");
        }

        private void LoadSelectedDayItems()
        {
            // Load tasks for selected day
            SelectedDayTasks.Clear();
            var tasks = DatabaseService.GetAllTasks()
                .Where(t => t.DueDate.Date == _selectedDate.Date)
                .OrderBy(t => (int)t.Priority) // Order by priority (High first)
                .ToList();
            
            foreach (var task in tasks)
            {
                SelectedDayTasks.Add(task);
            }
            
            // Load habits and check if they are tracked for the selected day
            SelectedDayHabits.Clear();
            var habits = DatabaseService.GetAllHabits();
            
            foreach (var habit in habits)
            {
                habit.IsSelectedDayTracked = habit.IsTrackedOnDate(_selectedDate);
                SelectedDayHabits.Add(habit);
            }
        }

        private void PreviousMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(-1);
            GenerateCalendar();
            LoadSelectedDayItems();
        }

        private void NextMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(1);
            GenerateCalendar();
            LoadSelectedDayItems();
        }

        private void HabitCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            var checkbox = sender as CheckBox;
            var habit = checkbox.DataContext as Habit;
            
            DatabaseService.TrackHabit(habit.Id, _selectedDate);
            LoadSelectedDayItems();
            GenerateCalendar(); // Regenerate to update indicators
        }

        private void HabitCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            var checkbox = sender as CheckBox;
            var habit = checkbox.DataContext as Habit;
            
            DatabaseService.UntrackHabit(habit.Id, _selectedDate);
            LoadSelectedDayItems();
            GenerateCalendar(); // Regenerate to update indicators
        }
    }

    public class CalendarDayViewModel
    {
        public string Day { get; set; }
        public DateTime? Date { get; set; }
        public Brush Background { get; set; }
        public Brush BorderBrush { get; set; }
        public Thickness BorderThickness { get; set; }
        public FontWeight DayFontWeight { get; set; }
        public List<DayIndicator> Indicators { get; set; } = new List<DayIndicator>();
    }

    public class DayIndicator
    {
        public Brush Color { get; set; }
    }
}