﻿#pragma checksum "..\..\..\..\..\Views\Pages\TasksPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "355825412E9BCF519C8E647D05B7FF48FF8CFC0D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using PersonalLifeManager.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PersonalLifeManager.Views.Pages {
    
    
    /// <summary>
    /// TasksPage
    /// </summary>
    public partial class TasksPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTaskButton;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TasksPanel;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoTasksText;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost TaskDialogHost;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DialogTitle;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaskTitleBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaskDescriptionBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker TaskDueDatePicker;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TaskPriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveTaskButton;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost DeleteDialogHost;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmDeleteButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PersonalLifeManager;component/views/pages/taskspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 11 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            ((PersonalLifeManager.Views.Pages.TasksPage)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Page_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 32 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            this.FilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 46 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            this.SearchBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AddTaskButton = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            this.AddTaskButton.Click += new System.Windows.RoutedEventHandler(this.AddTaskButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TasksPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.NoTasksText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TaskDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 8:
            this.DialogTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TaskTitleBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TaskDescriptionBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.TaskDueDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 12:
            this.TaskPriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.SaveTaskButton = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            this.SaveTaskButton.Click += new System.Windows.RoutedEventHandler(this.SaveTaskButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.DeleteDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 15:
            this.ConfirmDeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\..\..\Views\Pages\TasksPage.xaml"
            this.ConfirmDeleteButton.Click += new System.Windows.RoutedEventHandler(this.ConfirmDeleteButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

