using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views.Pages
{
    public partial class CoursesPage : Page
    {
        private List<Course> _allCourses;
        private Course _currentCourse;

        public CoursesPage()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadCourses();
        }

        private void LoadCourses()
        {
            try
            {
                // Get all courses from database
                _allCourses = DatabaseService.GetAllCourses();
                
                // Display courses
                DisplayCourses();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الكورسات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DisplayCourses()
        {
            // Clear current courses
            CoursesPanel.Children.Clear();
            
            if (_allCourses.Any())
            {
                NoCoursesText.Visibility = Visibility.Collapsed;
                
                // Sort courses by creation date (newest first)
                var sortedCourses = _allCourses.OrderByDescending(c => c.CreatedAt).ToList();
                
                // Add each course to the panel
                foreach (var course in sortedCourses)
                {
                    var courseCard = CreateCourseCard(course);
                    CoursesPanel.Children.Add(courseCard);
                }
            }
            else
            {
                NoCoursesText.Visibility = Visibility.Visible;
            }
        }

        private UIElement CreateCourseCard(Course course)
        {
            // Calculate progress percentage
            double progressPercentage = course.TotalLessons > 0 ? 
                (double)course.CompletedLessons / course.TotalLessons * 100 : 0;
            
            // Create card container
            var card = new Card
            {
                Width = 300,
                Margin = new Thickness(10),
                UniformCornerRadius = 8,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2D2D2D"))
            };
            
            // Create main grid
            var grid = new Grid { Margin = new Thickness(15) };
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            
            // Course title
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 10)
            };
            
            var titleText = new TextBlock
            {
                Text = course.Name,
                FontSize = 18,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0")),
                TextWrapping = TextWrapping.Wrap,
                Width = 240
            };
            titlePanel.Children.Add(titleText);
            
            // Delete button
            var deleteButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Padding = new Thickness(8),
                HorizontalAlignment = HorizontalAlignment.Right,
                ToolTip = "حذف",
                Margin = new Thickness(10, 0, 0, 0)
            };
            var deleteIcon = new PackIcon
            {
                Kind = PackIconKind.Delete,
                Width = 18,
                Height = 18,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5252"))
            };
            deleteButton.Content = deleteIcon;
            deleteButton.Click += (s, e) => DeleteCourse(course);
            titlePanel.Children.Add(deleteButton);
            
            Grid.SetRow(titlePanel, 0);
            grid.Children.Add(titlePanel);
            
            // Course source
            var sourceText = new TextBlock
            {
                Text = course.Source,
                FontSize = 14,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0")),
                Opacity = 0.7,
                Margin = new Thickness(0, 0, 0, 15),
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetRow(sourceText, 1);
            grid.Children.Add(sourceText);
            
            // Progress bar
            var progressBar = new ProgressBar
            {
                Value = progressPercentage,
                Height = 10,
                Margin = new Thickness(0, 0, 0, 10),
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319")),
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3D3D3D"))
            };
            Grid.SetRow(progressBar, 2);
            grid.Children.Add(progressBar);
            
            // Progress details and controls
            var detailsGrid = new Grid { Margin = new Thickness(0, 5, 0, 0) };
            detailsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            detailsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            
            // Progress text
            var progressText = new TextBlock
            {
                Text = $"{course.CompletedLessons} من {course.TotalLessons} درس ({progressPercentage:0}%)",
                FontSize = 14,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0")),
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(progressText, 0);
            detailsGrid.Children.Add(progressText);
            
            // Controls panel
            var controlsPanel = new StackPanel { Orientation = Orientation.Horizontal };
            Grid.SetColumn(controlsPanel, 1);
            
            // Decrement button
            var decrementButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Padding = new Thickness(8),
                Margin = new Thickness(0, 0, 5, 0),
                ToolTip = "إنقاص درس",
                IsEnabled = course.CompletedLessons > 0
            };
            var minusIcon = new PackIcon
            {
                Kind = PackIconKind.Minus,
                Width = 18,
                Height = 18
            };
            decrementButton.Content = minusIcon;
            decrementButton.Click += (s, e) => UpdateCourseProgress(course, -1);
            controlsPanel.Children.Add(decrementButton);
            
            // Increment button
            var incrementButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Padding = new Thickness(8),
                ToolTip = "زيادة درس",
                IsEnabled = course.CompletedLessons < course.TotalLessons
            };
            var plusIcon = new PackIcon
            {
                Kind = PackIconKind.Plus,
                Width = 18,
                Height = 18
            };
            incrementButton.Content = plusIcon;
            incrementButton.Click += (s, e) => UpdateCourseProgress(course, 1);
            controlsPanel.Children.Add(incrementButton);
            
            // Edit button
            var editButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                Padding = new Thickness(8),
                Margin = new Thickness(5, 0, 0, 0),
                ToolTip = "تعديل"
            };
            var editIcon = new PackIcon
            {
                Kind = PackIconKind.Pencil,
                Width = 18,
                Height = 18
            };
            editButton.Content = editIcon;
            editButton.Click += (s, e) => EditCourse(course);
            controlsPanel.Children.Add(editButton);
            
            detailsGrid.Children.Add(controlsPanel);
            Grid.SetRow(detailsGrid, 3);
            grid.Children.Add(detailsGrid);
            
            card.Content = grid;
            return card;
        }

        private void UpdateCourseProgress(Course course, int change)
        {
            try
            {
                // Update completed lessons
                int newValue = course.CompletedLessons + change;
                
                // Validate new value
                if (newValue < 0 || newValue > course.TotalLessons)
                    return;
                
                // Update course
                course.CompletedLessons = newValue;
                DatabaseService.UpdateCourse(course);
                
                // Refresh courses
                LoadCourses();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث تقدم الكورس: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddCourseButton_Click(object sender, RoutedEventArgs e)
        {
            // Reset dialog fields
            DialogTitle.Text = "إضافة كورس جديد";
            CourseNameBox.Text = string.Empty;
            CourseSourceBox.Text = string.Empty;
            TotalLessonsBox.Text = string.Empty;
            CompletedLessonsBox.Text = "0";
            CompletedLessonsBox.IsEnabled = false;
            
            // Set current course to null (new course)
            _currentCourse = null;
            
            // Show dialog
            CourseDialogHost.IsOpen = true;
        }

        private void EditCourse(Course course)
        {
            // Set dialog fields
            DialogTitle.Text = "تعديل الكورس";
            CourseNameBox.Text = course.Name;
            CourseSourceBox.Text = course.Source;
            TotalLessonsBox.Text = course.TotalLessons.ToString();
            CompletedLessonsBox.Text = course.CompletedLessons.ToString();
            CompletedLessonsBox.IsEnabled = true;
            
            // Set current course
            _currentCourse = course;
            
            // Show dialog
            CourseDialogHost.IsOpen = true;
        }

        private void DeleteCourse(Course course)
        {
            // Set current course
            _currentCourse = course;
            
            // Show delete confirmation dialog
            DeleteDialogHost.IsOpen = true;
        }

        private void SaveCourseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(CourseNameBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الكورس", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(CourseSourceBox.Text))
                {
                    MessageBox.Show("يرجى إدخال مصدر الكورس", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(TotalLessonsBox.Text) || !int.TryParse(TotalLessonsBox.Text, out int totalLessons) || totalLessons <= 0)
                {
                    MessageBox.Show("يرجى إدخال عدد صحيح للدروس الإجمالية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                int completedLessons = 0;
                if (!string.IsNullOrWhiteSpace(CompletedLessonsBox.Text))
                {
                    if (!int.TryParse(CompletedLessonsBox.Text, out completedLessons) || completedLessons < 0)
                    {
                        MessageBox.Show("يرجى إدخال عدد صحيح للدروس المكتملة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
                
                if (completedLessons > totalLessons)
                {
                    MessageBox.Show("عدد الدروس المكتملة لا يمكن أن يتجاوز العدد الإجمالي للدروس", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (_currentCourse == null) // New course
                {
                    // Create new course
                    var newCourse = new Course
                    {
                        Name = CourseNameBox.Text,
                        Source = CourseSourceBox.Text,
                        TotalLessons = totalLessons,
                        CompletedLessons = completedLessons,
                        CreatedAt = DateTime.Now
                    };
                    
                    // Save to database
                    DatabaseService.AddCourse(newCourse);
                }
                else // Update existing course
                {
                    // Update course properties
                    _currentCourse.Name = CourseNameBox.Text;
                    _currentCourse.Source = CourseSourceBox.Text;
                    _currentCourse.TotalLessons = totalLessons;
                    _currentCourse.CompletedLessons = completedLessons;
                    
                    // Save to database
                    DatabaseService.UpdateCourse(_currentCourse);
                }
                
                // Close dialog
                CourseDialogHost.IsOpen = false;
                
                // Refresh course list
                LoadCourses();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الكورس: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConfirmDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delete course from database
                DatabaseService.DeleteCourse(_currentCourse.Id);
                
                // Close dialog
                DeleteDialogHost.IsOpen = false;
                
                // Refresh course list
                LoadCourses();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الكورس: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            // Allow only digits
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }
    }
}