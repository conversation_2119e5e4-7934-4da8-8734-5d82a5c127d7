﻿#pragma checksum "..\..\..\..\..\Views\Pages\HabitsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7A1B610591D2E72C2FB51FEFBC3E86C47E1BC98B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using PersonalLifeManager.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PersonalLifeManager.Views.Pages {
    
    
    /// <summary>
    /// HabitsPage
    /// </summary>
    public partial class HabitsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 29 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousMonthButton;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthYearText;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddHabitButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel HabitsPanel;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoHabitsText;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost HabitDialogHost;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DialogTitle;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HabitNameBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HabitDescriptionBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveHabitButton;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost DeleteDialogHost;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmDeleteButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PersonalLifeManager;component/views/pages/habitspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 11 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
            ((PersonalLifeManager.Views.Pages.HabitsPage)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Page_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PreviousMonthButton = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.MonthYearText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.AddHabitButton = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
            this.AddHabitButton.Click += new System.Windows.RoutedEventHandler(this.AddHabitButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CalendarGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.HabitsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.NoHabitsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.HabitDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 10:
            this.DialogTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.HabitNameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.HabitDescriptionBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.SaveHabitButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
            this.SaveHabitButton.Click += new System.Windows.RoutedEventHandler(this.SaveHabitButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.DeleteDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 15:
            this.ConfirmDeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 188 "..\..\..\..\..\Views\Pages\HabitsPage.xaml"
            this.ConfirmDeleteButton.Click += new System.Windows.RoutedEventHandler(this.ConfirmDeleteButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

