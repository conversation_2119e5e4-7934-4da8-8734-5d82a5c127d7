using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views.Pages
{
    public partial class CalendarPage : Page
    {

        private DateTime _currentMonth;
        private List<TaskItem> _allTasks;
        private List<Habit> _allHabits;
        private readonly SolidColorBrush _accentBrush;
        private readonly SolidColorBrush _todayBrush;
        private readonly SolidColorBrush _taskBrush;
        private readonly SolidColorBrush _habitBrush;

        public CalendarPage()
        {
            InitializeComponent();

            _currentMonth = DateTime.Today;
            
            // Define brushes
            _accentBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319"));
            _todayBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319"));
            _taskBrush = new SolidColorBrush(Colors.Red);
            _habitBrush = new SolidColorBrush(Colors.Green);
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadData();
            GenerateCalendar();
        }

        private void LoadData()
        {
            // Load tasks and habits from database
            _allTasks = DatabaseService.GetAllTasks();
            _allHabits = DatabaseService.GetAllHabits();
        }

        private void GenerateCalendar()
        {
            // Update month/year display
            CultureInfo arCulture = new CultureInfo("ar-SA");
            string monthName = arCulture.DateTimeFormat.GetMonthName(_currentMonth.Month);
            MonthYearText.Text = $"{monthName} {_currentMonth.Year}";

            // Clear existing calendar days
            CalendarGrid.Children.Clear();

            // Get first day of month and total days
            DateTime firstDayOfMonth = new DateTime(_currentMonth.Year, _currentMonth.Month, 1);
            int daysInMonth = DateTime.DaysInMonth(_currentMonth.Year, _currentMonth.Month);
            
            // Calculate the day of week (0 = Sunday, 1 = Monday, etc.)
            int firstDayOfWeek = (int)firstDayOfMonth.DayOfWeek;

            // Generate calendar days
            int day = 1;
            for (int row = 0; row < 6; row++)
            {
                for (int col = 0; col < 7; col++)
                {
                    if ((row == 0 && col < firstDayOfWeek) || (day > daysInMonth))
                    {
                        // Empty cell
                        continue;
                    }
                    else
                    {
                        // Create day cell
                        DateTime currentDate = new DateTime(_currentMonth.Year, _currentMonth.Month, day);
                        var dayCell = CreateDayCell(currentDate);
                        
                        // Add to grid
                        Grid.SetRow(dayCell, row);
                        Grid.SetColumn(dayCell, col);
                        CalendarGrid.Children.Add(dayCell);
                        
                        day++;
                    }
                }
            }
        }

        private UIElement CreateDayCell(DateTime date)
        {
            // Create border for the day cell
            var border = new Border
            {
                Margin = new Thickness(2),
                CornerRadius = new CornerRadius(4),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(Colors.LightGray)
            };

            // Check if this is today
            bool isToday = date.Date == DateTime.Today;
            if (isToday)
            {
                border.BorderBrush = _todayBrush;
                border.BorderThickness = new Thickness(2);
            }

            // Create grid for day content
            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Day number
            var dayText = new TextBlock
            {
                Text = date.Day.ToString(),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 5, 0, 0),
                FontWeight = isToday ? FontWeights.Bold : FontWeights.Normal
            };
            Grid.SetRow(dayText, 0);
            grid.Children.Add(dayText);

            // Indicators for tasks and habits
            var indicatorsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 2, 0, 5)
            };
            Grid.SetRow(indicatorsPanel, 1);

            // Check for tasks on this day
            bool hasTasks = _allTasks.Any(t => t.DueDate.Date == date.Date);
            if (hasTasks)
            {
                var taskIndicator = new Ellipse
                {
                    Width = 8,
                    Height = 8,
                    Fill = _taskBrush,
                    Margin = new Thickness(2, 0, 2, 0)
                };
                indicatorsPanel.Children.Add(taskIndicator);
            }

            // Check for habit tracking on this day
            bool hasHabits = _allHabits.Any(h => h.TrackedDays.Any(td => td.Date == date.Date));
            if (hasHabits)
            {
                var habitIndicator = new Ellipse
                {
                    Width = 8,
                    Height = 8,
                    Fill = _habitBrush,
                    Margin = new Thickness(2, 0, 2, 0)
                };
                indicatorsPanel.Children.Add(habitIndicator);
            }

            grid.Children.Add(indicatorsPanel);
            border.Child = grid;

            // Make the day clickable
            border.MouseLeftButtonUp += (s, e) => ShowDayDetails(date);
            return border;
        }

        private void ShowDayDetails(DateTime date)
        {
            // Set selected date text
            CultureInfo arCulture = new CultureInfo("ar-SA");
            string dayName = arCulture.DateTimeFormat.GetDayName(date.DayOfWeek);
            string monthName = arCulture.DateTimeFormat.GetMonthName(date.Month);
            SelectedDateText.Text = $"{dayName} {date.Day} {monthName} {date.Year}";

            // Clear previous items
            DayItemsPanel.Children.Clear();
            DayItemsPanel.Children.Remove(NoDayItemsText);

            // Get tasks for this day
            var dayTasks = _allTasks.Where(t => t.DueDate.Date == date.Date).ToList();
            
            // Get habits tracked on this day
            var dayHabits = _allHabits.Where(h => h.TrackedDays.Any(td => td.Date == date.Date)).ToList();

            bool hasItems = false;

            // Add tasks section if there are tasks
            if (dayTasks.Any())
            {
                hasItems = true;
                var tasksHeader = new TextBlock
                {
                    Text = "المهام",
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                DayItemsPanel.Children.Add(tasksHeader);

                foreach (var task in dayTasks)
                {
                    var taskItem = CreateTaskItem(task);
                    DayItemsPanel.Children.Add(taskItem);
                }
            }

            // Add habits section if there are habits
            if (dayHabits.Any())
            {
                hasItems = true;
                var habitsHeader = new TextBlock
                {
                    Text = "العادات",
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 15, 0, 5)
                };
                DayItemsPanel.Children.Add(habitsHeader);

                foreach (var habit in dayHabits)
                {
                    var habitItem = CreateHabitItem(habit);
                    DayItemsPanel.Children.Add(habitItem);
                }
            }

            // Show "no items" message if needed
            if (!hasItems)
            {
                DayItemsPanel.Children.Add(NoDayItemsText);
            }

            // Open dialog
            DayDetailsDialogHost.IsOpen = true;
        }

        private UIElement CreateTaskItem(TaskItem task)
        {
            var grid = new Grid { Margin = new Thickness(0, 5, 0, 5) };
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Priority indicator
            var priorityIndicator = new Border
            {
                Width = 4,
                Height = 40,
                CornerRadius = new CornerRadius(2),
                Background = GetPriorityColor(task.Priority),
                Margin = new Thickness(0, 0, 10, 0)
            };
            Grid.SetColumn(priorityIndicator, 0);
            grid.Children.Add(priorityIndicator);

            // Task details
            var detailsPanel = new StackPanel();
            Grid.SetColumn(detailsPanel, 1);

            // Task title
            var titleText = new TextBlock
            {
                Text = task.Title,
                FontWeight = FontWeights.SemiBold,
                TextDecorations = task.IsCompleted ? TextDecorations.Strikethrough : null
            };
            detailsPanel.Children.Add(titleText);

            // Task description (if any)
            if (!string.IsNullOrWhiteSpace(task.Description))
            {
                var descriptionText = new TextBlock
                {
                    Text = task.Description,
                    TextWrapping = TextWrapping.Wrap,
                    Opacity = 0.7,
                    Margin = new Thickness(0, 5, 0, 0),
                    TextDecorations = task.IsCompleted ? TextDecorations.Strikethrough : null
                };
                detailsPanel.Children.Add(descriptionText);
            }

            grid.Children.Add(detailsPanel);
            return grid;
        }

        private UIElement CreateHabitItem(Habit habit)
        {
            var grid = new Grid { Margin = new Thickness(0, 5, 0, 5) };
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // Habit details
            var detailsPanel = new StackPanel();
            Grid.SetColumn(detailsPanel, 0);

            // Habit name
            var nameText = new TextBlock
            {
                Text = habit.Name,
                FontWeight = FontWeights.SemiBold
            };
            detailsPanel.Children.Add(nameText);

            // Habit description (if any)
            if (!string.IsNullOrWhiteSpace(habit.Description))
            {
                var descriptionText = new TextBlock
                {
                    Text = habit.Description,
                    TextWrapping = TextWrapping.Wrap,
                    Opacity = 0.7,
                    Margin = new Thickness(0, 5, 0, 0)
                };
                detailsPanel.Children.Add(descriptionText);
            }

            grid.Children.Add(detailsPanel);

            // Streak indicator
            var streakPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(streakPanel, 1);

            var fireIcon = new PackIcon
            {
                Kind = PackIconKind.Fire,
                Width = 16,
                Height = 16,
                Foreground = _accentBrush,
                VerticalAlignment = VerticalAlignment.Center
            };
            streakPanel.Children.Add(fireIcon);

            var streakText = new TextBlock
            {
                Text = habit.CurrentStreak.ToString(),
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(5, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            streakPanel.Children.Add(streakText);

            grid.Children.Add(streakPanel);

            return grid;
        }

        private SolidColorBrush GetPriorityColor(TaskPriority priority)
        {
            switch (priority)
            {
                case TaskPriority.High:
                    return new SolidColorBrush(Colors.Red);
                case TaskPriority.Medium:
                    return new SolidColorBrush(Colors.Orange);
                case TaskPriority.Low:
                    return new SolidColorBrush(Colors.Green);
                default:
                    return new SolidColorBrush(Colors.Gray);
            }
        }

        private void PreviousMonth_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(-1);
            GenerateCalendar();
        }

        private void CurrentMonth_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = DateTime.Today;
            GenerateCalendar();
        }

        private void NextMonth_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(1);
            GenerateCalendar();
        }
    }
}