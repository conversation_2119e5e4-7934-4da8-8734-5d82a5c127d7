using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views.Pages
{
    public partial class TasksPage : Page
    {
        private List<TaskItem> _allTasks;
        private TaskItem _currentEditTask;

        public TasksPage()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadTasks();
        }

        private void LoadTasks()
        {
            // Get all tasks from database
            _allTasks = DatabaseService.GetAllTasks();
            
            // Apply current filter
            ApplyFilter();
        }

        private void ApplyFilter()
        {
            // Get selected filter
            string filter = ((ComboBoxItem)FilterComboBox.SelectedItem)?.Content.ToString() ?? "الكل";
            string searchText = SearchBox.Text.ToLower();
            
            // Apply filter
            var filteredTasks = _allTasks;
            
            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filteredTasks = filteredTasks.Where(t => 
                    t.Title.ToLower().Contains(searchText) || 
                    (t.Description != null && t.Description.ToLower().Contains(searchText))
                ).ToList();
            }
            
            // Apply status/priority filter
            switch (filter)
            {
                case "قيد التنفيذ":
                    filteredTasks = filteredTasks.Where(t => !t.IsCompleted).ToList();
                    break;
                case "مكتملة":
                    filteredTasks = filteredTasks.Where(t => t.IsCompleted).ToList();
                    break;
                case "أولوية عالية":
                    filteredTasks = filteredTasks.Where(t => t.Priority == TaskPriority.High).ToList();
                    break;
                case "اليوم":
                    filteredTasks = filteredTasks.Where(t => t.DueDate?.Date == DateTime.Today).ToList();
                    break;
                case "هذا الأسبوع":
                    var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                    var endOfWeek = startOfWeek.AddDays(6);
                    filteredTasks = filteredTasks.Where(t => 
                        t.DueDate >= startOfWeek && t.DueDate <= endOfWeek
                    ).ToList();
                    break;
            }
            
            // Display filtered tasks
            DisplayTasks(filteredTasks);
        }

        private void DisplayTasks(List<TaskItem> tasks)
        {
            // Clear current tasks
            TasksPanel.Children.Clear();
            
            if (tasks.Any())
            {
                NoTasksText.Visibility = Visibility.Collapsed;
                
                // Group tasks by completion status
                var pendingTasks = tasks.Where(t => !t.IsCompleted).OrderBy(t => t.DueDate).ToList();
                var completedTasks = tasks.Where(t => t.IsCompleted).OrderByDescending(t => t.DueDate).ToList();
                
                // Add pending tasks
                if (pendingTasks.Any())
                {
                    var pendingHeader = new TextBlock
                    {
                        Text = "المهام قيد التنفيذ",
                        FontSize = 16,
                        FontWeight = FontWeights.Medium,
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    TasksPanel.Children.Add(pendingHeader);
                    
                    foreach (var task in pendingTasks)
                    {
                        var taskCard = CreateTaskCard(task);
                        TasksPanel.Children.Add(taskCard);
                    }
                }
                
                // Add completed tasks
                if (completedTasks.Any())
                {
                    var completedHeader = new TextBlock
                    {
                        Text = "المهام المكتملة",
                        FontSize = 16,
                        FontWeight = FontWeights.Medium,
                        Margin = new Thickness(0, 20, 0, 10)
                    };
                    TasksPanel.Children.Add(completedHeader);
                    
                    foreach (var task in completedTasks)
                    {
                        var taskCard = CreateTaskCard(task);
                        TasksPanel.Children.Add(taskCard);
                    }
                }
            }
            else
            {
                NoTasksText.Visibility = Visibility.Visible;
                TasksPanel.Children.Add(NoTasksText);
            }
        }

        private UIElement CreateTaskCard(TaskItem task)
        {
            var card = new Card
            {
                Margin = new Thickness(0, 0, 0, 10),
                UniformCornerRadius = 8
            };

            var grid = new Grid { Margin = new Thickness(15) };
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // Checkbox for completion status
            var checkBox = new CheckBox
            {
                IsChecked = task.IsCompleted,
                VerticalAlignment = VerticalAlignment.Top,
                Margin = new Thickness(0, 0, 10, 0)
            };
            checkBox.Checked += (s, e) => ToggleTaskCompletion(task, true);
            checkBox.Unchecked += (s, e) => ToggleTaskCompletion(task, false);
            Grid.SetColumn(checkBox, 0);
            grid.Children.Add(checkBox);

            // Task content
            var contentPanel = new StackPanel { Margin = new Thickness(5, 0, 0, 0) };
            Grid.SetColumn(contentPanel, 1);

            // Task title
            var titleText = new TextBlock
            {
                Text = task.Title,
                FontSize = 16,
                TextWrapping = TextWrapping.Wrap,
                TextDecorations = task.IsCompleted ? TextDecorations.Strikethrough : null
            };
            contentPanel.Children.Add(titleText);

            // Task description (if any)
            if (!string.IsNullOrWhiteSpace(task.Description))
            {
                var descriptionText = new TextBlock
                {
                    Text = task.Description,
                    FontSize = 12,
                    TextWrapping = TextWrapping.Wrap,
                    Opacity = 0.7,
                    Margin = new Thickness(0, 5, 0, 0)
                };
                contentPanel.Children.Add(descriptionText);
            }

            // Task metadata
            var metadataPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 0)
            };

            // Due date
            if (task.DueDate.HasValue)
            {
                var dueDateText = new TextBlock
                {
                    Text = task.DueDateText,
                    FontSize = 12,
                    Margin = new Thickness(0, 0, 15, 0)
                };
                metadataPanel.Children.Add(dueDateText);
            }

            // Priority
            var priorityText = new TextBlock
            {
                Text = task.PriorityText,
                FontSize = 12,
                Foreground = GetPriorityColor(task.Priority)
            };
            metadataPanel.Children.Add(priorityText);

            contentPanel.Children.Add(metadataPanel);
            grid.Children.Add(contentPanel);

            // Action buttons
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                VerticalAlignment = VerticalAlignment.Top
            };
            Grid.SetColumn(actionsPanel, 2);

            // Edit button
            var editButton = new Button
            {
                Style = (Style)FindResource("MaterialDesignFlatButton"),
                ToolTip = "تعديل",
                Width = 30,
                Height = 30,
                Padding = new Thickness(0),
                Content = new PackIcon { Kind = PackIconKind.Pencil, Width = 18, Height = 18 }
            };
            editButton.Click += (s, e) => OpenEditTaskDialog(task);
            actionsPanel.Children.Add(editButton);

            // Delete button
            var deleteButton = new Button
            {
                Style = (Style)FindResource("MaterialDesignFlatButton"),
                ToolTip = "حذف",
                Width = 30,
                Height = 30,
                Padding = new Thickness(0),
                Content = new PackIcon { Kind = PackIconKind.Delete, Width = 18, Height = 18 }
            };
            deleteButton.Click += (s, e) => OpenDeleteTaskDialog(task);
            actionsPanel.Children.Add(deleteButton);

            grid.Children.Add(actionsPanel);
            card.Content = grid;

            return card;
        }

        private Brush GetPriorityColor(TaskPriority priority)
        {
            switch (priority)
            {
                case TaskPriority.High:
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5252"));
                case TaskPriority.Medium:
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319"));
                default:
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"));
            }
        }

        private void ToggleTaskCompletion(TaskItem task, bool isCompleted)
        {
            task.IsCompleted = isCompleted;
            DatabaseService.UpdateTask(task);
            
            // Refresh the task list
            ApplyFilter();
        }

        private void AddTaskButton_Click(object sender, RoutedEventArgs e)
        {
            // Reset dialog fields
            DialogTitle.Text = "إضافة مهمة جديدة";
            TaskTitleBox.Text = string.Empty;
            TaskDescriptionBox.Text = string.Empty;
            TaskDueDatePicker.SelectedDate = DateTime.Today;
            TaskPriorityComboBox.SelectedIndex = 1; // Medium priority by default
            
            // Clear current edit task
            _currentEditTask = null;
            
            // Open dialog
            TaskDialogHost.IsOpen = true;
        }

        private void OpenEditTaskDialog(TaskItem task)
        {
            // Set dialog fields
            DialogTitle.Text = "تعديل المهمة";
            TaskTitleBox.Text = task.Title;
            TaskDescriptionBox.Text = task.Description ?? string.Empty;
            TaskDueDatePicker.SelectedDate = task.DueDate;
            TaskPriorityComboBox.SelectedIndex = (int)task.Priority;
            
            // Set current edit task
            _currentEditTask = task;
            
            // Open dialog
            TaskDialogHost.IsOpen = true;
        }

        private void OpenDeleteTaskDialog(TaskItem task)
        {
            // Set current edit task
            _currentEditTask = task;
            
            // Open dialog
            DeleteDialogHost.IsOpen = true;
        }

        private void SaveTaskButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(TaskTitleBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان المهمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            // Get task data
            string title = TaskTitleBox.Text.Trim();
            string description = TaskDescriptionBox.Text.Trim();
            DateTime? dueDate = TaskDueDatePicker.SelectedDate;
            TaskPriority priority = (TaskPriority)int.Parse(((ComboBoxItem)TaskPriorityComboBox.SelectedItem).Tag.ToString());
            
            if (_currentEditTask == null)
            {
                // Create new task
                var newTask = new TaskItem
                {
                    Title = title,
                    Description = string.IsNullOrWhiteSpace(description) ? null : description,
                    DueDate = dueDate,
                    Priority = priority,
                    IsCompleted = false,
                    CreatedAt = DateTime.Now
                };
                
                DatabaseService.AddTask(newTask);
            }
            else
            {
                // Update existing task
                _currentEditTask.Title = title;
                _currentEditTask.Description = string.IsNullOrWhiteSpace(description) ? null : description;
                _currentEditTask.DueDate = dueDate;
                _currentEditTask.Priority = priority;
                
                DatabaseService.UpdateTask(_currentEditTask);
            }
            
            // Close dialog
            TaskDialogHost.IsOpen = false;
            
            // Refresh tasks
            LoadTasks();
        }

        private void ConfirmDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditTask != null)
            {
                // Delete task
                DatabaseService.DeleteTask(_currentEditTask.Id);
                
                // Close dialog
                DeleteDialogHost.IsOpen = false;
                
                // Refresh tasks
                LoadTasks();
            }
        }

        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilter();
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilter();
        }
    }
}