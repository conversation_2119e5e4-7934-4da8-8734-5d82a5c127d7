<Application x:Class="PersonalLifeManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:PersonalLifeManager"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="DeepOrange" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Custom Colors -->
            <Color x:Key="PrimaryBackgroundColor">#262626</Color>
            <Color x:Key="PrimaryTextColor">#F0F0F0</Color>
            <Color x:Key="AccentColor">#F39319</Color>
            
            <!-- Brushes -->
            <SolidColorBrush x:Key="PrimaryBackgroundBrush" Color="{StaticResource PrimaryBackgroundColor}"/>
            <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
            <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
            
            <!-- Converters -->
            <local:BrushOpacityConverter x:Key="BrushOpacityConverter" />
            <local:BrushLightenConverter x:Key="BrushLightenConverter" />
            
            <!-- TextBox Style with Animation -->
            <Style x:Key="AnimatedTextBox" TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="Padding" Value="8,6" />
                <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="materialDesign:HintAssist.Foreground" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True" />
                <Style.Triggers>
                    <EventTrigger RoutedEvent="GotFocus">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                 From="1" To="1.02" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                 From="1" To="1.02" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                    <EventTrigger RoutedEvent="LostFocus">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                 From="1.02" To="1" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                 From="1.02" To="1" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1" />
                    </Setter.Value>
                </Setter>
                </Style.Triggers>
            </Style>
            
            <!-- PasswordBox Style with Animation -->
            <Style x:Key="AnimatedPasswordBox" TargetType="{x:Type PasswordBox}" BasedOn="{StaticResource MaterialDesignPasswordBox}">
                <Setter Property="Padding" Value="8,6" />
                <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="materialDesign:HintAssist.Foreground" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1" />
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <EventTrigger RoutedEvent="GotFocus">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                 From="1" To="1.02" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                 From="1" To="1.02" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                    <EventTrigger RoutedEvent="LostFocus">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                 From="1.02" To="1" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                 From="1.02" To="1" Duration="0:0:0.2">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Style.Triggers>
            </Style>
            
            <!-- Styles -->
            <!-- Dialog Host Style -->
            <Style TargetType="materialDesign:DialogHost">
                <Setter Property="DialogTheme" Value="Inherit" />
                <Setter Property="DialogMargin" Value="16" />
                <Setter Property="CornerRadius" Value="8" />
            </Style>
            
            <Style x:Key="PageTitle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                <Setter Property="Margin" Value="0,0,0,20"/>
            </Style>
            
            <Style x:Key="SectionTitle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                <Setter Property="Margin" Value="0,10,0,10"/>
            </Style>
            
            <Style x:Key="NavigationButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="Height" Value="48"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="Padding" Value="16,0"/>
                <Setter Property="Margin" Value="0,2"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
            
            <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="Margin" Value="5" />
                <Setter Property="Padding" Value="16,8" />
                <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}" />
                <Setter Property="Foreground" Value="{StaticResource AccentBrush}" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1" />
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <EventTrigger RoutedEvent="Button.Click">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                             From="1" To="0.95" Duration="0:0:0.1" AutoReverse="True">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                             From="1" To="0.95" Duration="0:0:0.1" AutoReverse="True">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#10F39319" />
                    </Trigger>
                </Style.Triggers>
            </Style>
            
            <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1" />
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <EventTrigger RoutedEvent="Button.Click">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                 From="1" To="0.95" Duration="0:0:0.1" AutoReverse="True">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                 From="1" To="0.95" Duration="0:0:0.1" AutoReverse="True">
                                    <DoubleAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </DoubleAnimation.EasingFunction>
                                </DoubleAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                 </Style.Triggers>
                </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>