using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;
using PersonalLifeManager.Models;

namespace PersonalLifeManager.Services
{
    public static class DatabaseService
    {
        private static readonly string DbPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "PersonalLifeManager", "plm_database.db");

        private static readonly string ConnectionString = $"Data Source={DbPath};Version=3;";

        public static void Initialize()
        {
            bool newDatabase = !File.Exists(DbPath);
            
            if (newDatabase)
            {
                SQLiteConnection.CreateFile(DbPath);
                CreateTables();
            }
        }

        private static void CreateTables()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();

                // Create Tasks table
                string createTasksTable = @"
                    CREATE TABLE IF NOT EXISTS Tasks (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Title TEXT NOT NULL,
                        Description TEXT,
                        DueDate TEXT,
                        Priority INTEGER,
                        IsCompleted INTEGER DEFAULT 0,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                    );
                ";

                // Create Courses table
                string createCoursesTable = @"
                    CREATE TABLE IF NOT EXISTS Courses (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Source TEXT,
                        TotalLessons INTEGER,
                        CompletedLessons INTEGER DEFAULT 0,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                    );
                ";

                // Create Habits table
                string createHabitsTable = @"
                    CREATE TABLE IF NOT EXISTS Habits (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                    );
                ";

                // Create HabitTracking table
                string createHabitTrackingTable = @"
                    CREATE TABLE IF NOT EXISTS HabitTracking (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        HabitId INTEGER,
                        Date TEXT,
                        FOREIGN KEY (HabitId) REFERENCES Habits(Id) ON DELETE CASCADE
                    );
                ";

                // Create User table with default password
                string createUserTable = @"
                    CREATE TABLE IF NOT EXISTS User (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Password TEXT NOT NULL
                    );
                    
                    INSERT INTO User (Password) VALUES ('1141004');
                ";

                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = createTasksTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createCoursesTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createHabitsTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createHabitTrackingTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createUserTable;
                    command.ExecuteNonQuery();
                }
            }
        }

        #region Tasks Methods

        public static List<TaskItem> GetAllTasks()
        {
            var tasks = new List<TaskItem>();

            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Tasks ORDER BY DueDate, Priority";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        tasks.Add(new TaskItem
                        {
                            Id = reader.GetInt32(0),
                            Title = reader.GetString(1),
                            Description = reader.IsDBNull(2) ? null : reader.GetString(2),
                            DueDate = reader.IsDBNull(3) ? null : DateTime.Parse(reader.GetString(3)),
                            Priority = (TaskPriority)reader.GetInt32(4),
                            IsCompleted = reader.GetInt32(5) == 1,
                            CreatedAt = DateTime.Parse(reader.GetString(6))
                        });
                    }
                }
            }

            return tasks;
        }

        public static List<TaskItem> GetTasksByDate(DateTime date)
        {
            var tasks = new List<TaskItem>();
            string dateString = date.ToString("yyyy-MM-dd");

            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Tasks WHERE date(DueDate) = date(@Date) ORDER BY Priority";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Date", dateString);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            tasks.Add(new TaskItem
                            {
                                Id = reader.GetInt32(0),
                                Title = reader.GetString(1),
                                Description = reader.IsDBNull(2) ? null : reader.GetString(2),
                                DueDate = reader.IsDBNull(3) ? null : DateTime.Parse(reader.GetString(3)),
                                Priority = (TaskPriority)reader.GetInt32(4),
                                IsCompleted = reader.GetInt32(5) == 1,
                                CreatedAt = DateTime.Parse(reader.GetString(6))
                            });
                        }
                    }
                }
            }

            return tasks;
        }

        public static void AddTask(TaskItem task)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = @"
                    INSERT INTO Tasks (Title, Description, DueDate, Priority, IsCompleted)
                    VALUES (@Title, @Description, @DueDate, @Priority, @IsCompleted);
                    SELECT last_insert_rowid();
                ";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Title", task.Title);
                    command.Parameters.AddWithValue("@Description", task.Description as object ?? DBNull.Value);
                    command.Parameters.AddWithValue("@DueDate", task.DueDate?.ToString("yyyy-MM-dd HH:mm:ss") as object ?? DBNull.Value);
                    command.Parameters.AddWithValue("@Priority", (int)task.Priority);
                    command.Parameters.AddWithValue("@IsCompleted", task.IsCompleted ? 1 : 0);

                    task.Id = Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateTask(TaskItem task)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE Tasks
                    SET Title = @Title,
                        Description = @Description,
                        DueDate = @DueDate,
                        Priority = @Priority,
                        IsCompleted = @IsCompleted
                    WHERE Id = @Id
                ";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", task.Id);
                    command.Parameters.AddWithValue("@Title", task.Title);
                    command.Parameters.AddWithValue("@Description", task.Description as object ?? DBNull.Value);
                    command.Parameters.AddWithValue("@DueDate", task.DueDate?.ToString("yyyy-MM-dd HH:mm:ss") as object ?? DBNull.Value);
                    command.Parameters.AddWithValue("@Priority", (int)task.Priority);
                    command.Parameters.AddWithValue("@IsCompleted", task.IsCompleted ? 1 : 0);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteTask(int taskId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "DELETE FROM Tasks WHERE Id = @Id";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", taskId);
                    command.ExecuteNonQuery();
                }
            }
        }

        #endregion

        #region Courses Methods

        public static List<Course> GetAllCourses()
        {
            var courses = new List<Course>();

            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Courses ORDER BY Name";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        courses.Add(new Course
                        {
                            Id = reader.GetInt32(0),
                            Name = reader.GetString(1),
                            Source = reader.IsDBNull(2) ? null : reader.GetString(2),
                            TotalLessons = reader.GetInt32(3),
                            CompletedLessons = reader.GetInt32(4),
                            CreatedAt = DateTime.Parse(reader.GetString(5))
                        });
                    }
                }
            }

            return courses;
        }

        public static void AddCourse(Course course)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = @"
                    INSERT INTO Courses (Name, Source, TotalLessons, CompletedLessons)
                    VALUES (@Name, @Source, @TotalLessons, @CompletedLessons);
                    SELECT last_insert_rowid();
                ";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", course.Name);
                    command.Parameters.AddWithValue("@Source", course.Source as object ?? DBNull.Value);
                    command.Parameters.AddWithValue("@TotalLessons", course.TotalLessons);
                    command.Parameters.AddWithValue("@CompletedLessons", course.CompletedLessons);

                    course.Id = Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateCourse(Course course)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE Courses
                    SET Name = @Name,
                        Source = @Source,
                        TotalLessons = @TotalLessons,
                        CompletedLessons = @CompletedLessons
                    WHERE Id = @Id
                ";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", course.Id);
                    command.Parameters.AddWithValue("@Name", course.Name);
                    command.Parameters.AddWithValue("@Source", course.Source as object ?? DBNull.Value);
                    command.Parameters.AddWithValue("@TotalLessons", course.TotalLessons);
                    command.Parameters.AddWithValue("@CompletedLessons", course.CompletedLessons);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteCourse(int courseId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "DELETE FROM Courses WHERE Id = @Id";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", courseId);
                    command.ExecuteNonQuery();
                }
            }
        }

        #endregion

        #region Habits Methods

        public static List<Habit> GetAllHabits()
        {
            var habits = new List<Habit>();

            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Habits ORDER BY Name";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var habit = new Habit
                        {
                            Id = reader.GetInt32(0),
                            Name = reader.GetString(1),
                            Description = reader.IsDBNull(2) ? null : reader.GetString(2),
                            CreatedAt = DateTime.Parse(reader.GetString(3)),
                            TrackingDates = new List<DateTime>()
                        };

                        // Get tracking dates for this habit
                        habits.Add(habit);
                    }
                }

                // Load tracking dates for each habit
                foreach (var habit in habits)
                {
                    string trackingQuery = "SELECT Date FROM HabitTracking WHERE HabitId = @HabitId";
                    using (var command = new SQLiteCommand(trackingQuery, connection))
                    {
                        command.Parameters.AddWithValue("@HabitId", habit.Id);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                habit.TrackingDates.Add(DateTime.Parse(reader.GetString(0)));
                            }
                        }
                    }
                }
            }

            return habits;
        }

        public static void AddHabit(Habit habit)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = @"
                    INSERT INTO Habits (Name, Description)
                    VALUES (@Name, @Description);
                    SELECT last_insert_rowid();
                ";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", habit.Name);
                    command.Parameters.AddWithValue("@Description", habit.Description as object ?? DBNull.Value);

                    habit.Id = Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateHabit(Habit habit)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE Habits
                    SET Name = @Name,
                        Description = @Description
                    WHERE Id = @Id
                ";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", habit.Id);
                    command.Parameters.AddWithValue("@Name", habit.Name);
                    command.Parameters.AddWithValue("@Description", habit.Description as object ?? DBNull.Value);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteHabit(int habitId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "DELETE FROM Habits WHERE Id = @Id";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", habitId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void TrackHabit(int habitId, DateTime date)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();

                // Check if this date is already tracked
                string checkQuery = "SELECT COUNT(*) FROM HabitTracking WHERE HabitId = @HabitId AND Date = @Date";
                bool exists = false;

                using (var command = new SQLiteCommand(checkQuery, connection))
                {
                    command.Parameters.AddWithValue("@HabitId", habitId);
                    command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                    exists = Convert.ToInt32(command.ExecuteScalar()) > 0;
                }

                if (!exists)
                {
                    string insertQuery = "INSERT INTO HabitTracking (HabitId, Date) VALUES (@HabitId, @Date)";
                    using (var command = new SQLiteCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@HabitId", habitId);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        command.ExecuteNonQuery();
                    }
                }
            }
        }

        public static void TrackHabit(int habitId, DateTime date, bool isTracked)
        {
            if (isTracked)
            {
                TrackHabit(habitId, date);
            }
            else
            {
                UntrackHabit(habitId, date);
            }
        }

        public static void UntrackHabit(int habitId, DateTime date)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string deleteQuery = "DELETE FROM HabitTracking WHERE HabitId = @HabitId AND Date = @Date";
                using (var command = new SQLiteCommand(deleteQuery, connection))
                {
                    command.Parameters.AddWithValue("@HabitId", habitId);
                    command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                    command.ExecuteNonQuery();
                }
            }
        }

        #endregion

        #region Authentication

        public static bool ValidatePassword(string password)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM User WHERE Password = @Password";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Password", password);
                    int count = Convert.ToInt32(command.ExecuteScalar());
                    return count > 0;
                }
            }
        }

        #endregion
    }
}