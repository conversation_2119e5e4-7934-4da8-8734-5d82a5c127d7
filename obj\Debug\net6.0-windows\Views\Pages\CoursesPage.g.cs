﻿#pragma checksum "..\..\..\..\..\Views\Pages\CoursesPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8E2FDD606061A371F62009512EDA93CCA3421F44"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using PersonalLifeManager.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PersonalLifeManager.Views.Pages {
    
    
    /// <summary>
    /// CoursesPage
    /// </summary>
    public partial class CoursesPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 33 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCourseButton;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoCoursesText;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel CoursesPanel;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost CourseDialogHost;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DialogTitle;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CourseNameBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CourseSourceBox;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TotalLessonsBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompletedLessonsBox;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveCourseButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost DeleteDialogHost;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmDeleteButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PersonalLifeManager;component/views/pages/coursespage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 10 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            ((PersonalLifeManager.Views.Pages.CoursesPage)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Page_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddCourseButton = ((System.Windows.Controls.Button)(target));
            
            #line 43 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            this.AddCourseButton.Click += new System.Windows.RoutedEventHandler(this.AddCourseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.NoCoursesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CoursesPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 5:
            this.CourseDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 6:
            this.DialogTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CourseNameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.CourseSourceBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.TotalLessonsBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 112 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            this.TotalLessonsBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumberValidationTextBox);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CompletedLessonsBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 121 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            this.CompletedLessonsBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumberValidationTextBox);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SaveCourseButton = ((System.Windows.Controls.Button)(target));
            
            #line 139 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            this.SaveCourseButton.Click += new System.Windows.RoutedEventHandler(this.SaveCourseButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DeleteDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 13:
            this.ConfirmDeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\..\..\..\Views\Pages\CoursesPage.xaml"
            this.ConfirmDeleteButton.Click += new System.Windows.RoutedEventHandler(this.ConfirmDeleteButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

