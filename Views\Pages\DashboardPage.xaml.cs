using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views.Pages
{
    public partial class DashboardPage : Page
    {
        public DashboardPage()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadStatistics();
            LoadUpcomingTasks();
            LoadTodayHabits();
        }

        private void LoadStatistics()
        {
            // Load tasks statistics
            var tasks = DatabaseService.GetAllTasks();
            int totalTasks = tasks.Count;
            int completedTasks = tasks.Count(t => t.IsCompleted);
            
            TasksCountText.Text = totalTasks.ToString();
            TasksCompletedText.Text = $"{completedTasks} مكتملة";

            // Load courses statistics
            var courses = DatabaseService.GetAllCourses();
            int totalCourses = courses.Count;
            double averageProgress = totalCourses > 0 
                ? courses.Average(c => c.ProgressPercentage) 
                : 0;
            
            CoursesCountText.Text = totalCourses.ToString();
            CoursesProgressText.Text = $"{averageProgress:0.0}% مكتمل";

            // Load habits statistics
            var habits = DatabaseService.GetAllHabits();
            int totalHabits = habits.Count;
            int trackedToday = habits.Count(h => h.IsTrackedToday);
            
            HabitsCountText.Text = totalHabits.ToString();
            HabitsTrackedTodayText.Text = $"{trackedToday} متابعة اليوم";

            // Load today's events
            var todayTasks = tasks.Count(t => t.DueDate?.Date == DateTime.Today);
            TodayEventsText.Text = $"{todayTasks} أحداث اليوم";
        }

        private void LoadUpcomingTasks()
        {
            var tasks = DatabaseService.GetAllTasks()
                .Where(t => !t.IsCompleted && t.DueDate.HasValue)
                .OrderBy(t => t.DueDate)
                .Take(5)
                .ToList();

            UpcomingTasksPanel.Children.Clear();
            
            if (tasks.Any())
            {
                NoTasksText.Visibility = Visibility.Collapsed;
                
                foreach (var task in tasks)
                {
                    var taskCard = CreateTaskCard(task);
                    UpcomingTasksPanel.Children.Add(taskCard);
                }
            }
            else
            {
                NoTasksText.Visibility = Visibility.Visible;
                UpcomingTasksPanel.Children.Add(NoTasksText);
            }
        }

        private void LoadTodayHabits()
        {
            var habits = DatabaseService.GetAllHabits();

            TodayHabitsPanel.Children.Clear();
            
            if (habits.Any())
            {
                NoHabitsText.Visibility = Visibility.Collapsed;
                
                foreach (var habit in habits)
                {
                    var habitCard = CreateHabitCard(habit);
                    TodayHabitsPanel.Children.Add(habitCard);
                }
            }
            else
            {
                NoHabitsText.Visibility = Visibility.Visible;
                TodayHabitsPanel.Children.Add(NoHabitsText);
            }
        }

        private UIElement CreateTaskCard(TaskItem task)
        {
            var card = new Card
            {
                Margin = new Thickness(0, 0, 0, 10),
                UniformCornerRadius = 8,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333333"))
            };

            var grid = new Grid { Margin = new Thickness(15) };
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Task title
            var titleText = new TextBlock
            {
                Text = task.Title,
                FontSize = 16,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0"))
            };
            Grid.SetRow(titleText, 0);
            grid.Children.Add(titleText);

            // Task details
            var detailsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 0)
            };
            Grid.SetRow(detailsPanel, 1);

            // Due date
            var dueDateText = new TextBlock
            {
                Text = task.DueDateText,
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0")),
                Opacity = 0.7,
                Margin = new Thickness(0, 0, 15, 0)
            };
            detailsPanel.Children.Add(dueDateText);

            // Priority
            var priorityText = new TextBlock
            {
                Text = task.PriorityText,
                FontSize = 12,
                Foreground = GetPriorityColor(task.Priority),
                Margin = new Thickness(0, 0, 15, 0)
            };
            detailsPanel.Children.Add(priorityText);

            grid.Children.Add(detailsPanel);
            card.Content = grid;

            return card;
        }

        private UIElement CreateHabitCard(Habit habit)
        {
            var card = new Card
            {
                Margin = new Thickness(0, 0, 0, 10),
                UniformCornerRadius = 8,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333333"))
            };

            var grid = new Grid { Margin = new Thickness(15) };
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // Habit name
            var nameText = new TextBlock
            {
                Text = habit.Name,
                FontSize = 16,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0")),
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(nameText, 0);
            grid.Children.Add(nameText);

            // Checkbox
            var checkBox = new CheckBox
            {
                IsChecked = habit.IsTrackedToday,
                Style = Application.Current.Resources["MaterialDesignCheckBox"] as Style,
                VerticalAlignment = VerticalAlignment.Center
            };
            checkBox.Checked += (s, e) => TrackHabit(habit, true);
            checkBox.Unchecked += (s, e) => TrackHabit(habit, false);
            Grid.SetColumn(checkBox, 1);
            grid.Children.Add(checkBox);

            card.Content = grid;
            return card;
        }

        private void TrackHabit(Habit habit, bool isTracked)
        {
            if (isTracked)
            {
                DatabaseService.TrackHabit(habit.Id, DateTime.Today);
            }
            else
            {
                DatabaseService.UntrackHabit(habit.Id, DateTime.Today);
            }
            
            // Refresh statistics
            LoadStatistics();
        }

        private Brush GetPriorityColor(TaskPriority priority)
        {
            switch (priority)
            {
                case TaskPriority.High:
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5252"));
                case TaskPriority.Medium:
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319"));
                default:
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"));
            }
        }
    }
}